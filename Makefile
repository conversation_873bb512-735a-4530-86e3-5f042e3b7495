.DEFAULT_GOAL := help
.EXPORT_ALL_VARIABLES:
DOCKER_NETWORK := stack
COVERAGE := 60
DOCKER_BUILDKIT := 1
COMPOSE_DOCKER_CLI_BUILD := 1
RUN_ENV ?= local

archive: ## Export project folder to tar format archive
	git archive --format=tar.gz -o ../foundation.tar.gz --prefix=foundation/ develop

build_lambdas:
	docker compose --profile $(RUN_ENV) -f services/serverless/apps/docker-compose.yaml build

build_lambdas_test_handler:
	docker compose --profile $(RUN_ENV) -f services/serverless/apps/docker-compose.yaml build test_handler

clean: ## Cleans all project docker images and removes containers
	docker compose --profile $(RUN_ENV) down -v --rmi all
	docker compose --profile $(RUN_ENV) -f infrastructure/docker-compose.yaml down --remove-orphans -v --rmi all

deploy_lambdas: ## Deploy serverless lambdas
	cd services/serverless/apps && NODE_ENV=$(RUN_ENV) serverless deploy --stage=$(RUN_ENV)

docker_buildkit_efficacy: ## Checks the docker buildkit cache efficacy
	docker system df -v --format '{{ .BuildCache | json }}' | jq -c '[ .[] | select ( .CacheType == "exec.cachemount" )]' | jq .

down: ## Stops and removes project docker containers
	docker compose --profile $(RUN_ENV) down --remove-orphans
	docker compose --profile $(RUN_ENV) -f infrastructure/docker-compose.yaml down

export_openapi:  ## Export OpenAPI stable schema
	docker run --rm --network $(DOCKER_NETWORK) appropriate/curl -s http://data_service:8003/openapi-stable.json -o services/data_service/openapi-stable.json

generate_access_token: ## Generates long living access token for local development
	docker exec data_service python3 -m services.base.api.authentication.generate_access_token

get_os_schema: ## Runs the get_schema script to export current OpenSearch schema
	echo "Running get_schema"
	python3 -m services.base.infrastructure.database.opensearch.get_schema

help: ## Show this help (runs only in bash)
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

launch: launch_infrastructure provision_infrastructure launch_api ## Launches whole project in docker
	docker compose --profile $(RUN_ENV) up -d

launch_infrastructure: ## Launches self-hosted infrastructure in docker
	docker compose --profile $(RUN_ENV) -f infrastructure/docker-compose.yaml up -d

launch_api: ## Launches api services in docker
	docker compose --profile $(RUN_ENV) up -d

load_samples: ## Creates new indices and loads development samples into OpenSearch database
	docker exec file_service python3 -m services.file_service.application.loaders.run_loaders
	docker exec data_service python3 -m services.data_service.application.seeders.run_seeders
	docker exec mobile_service python3 -m services.mobile_service.application.use_cases.run_loaders

lint: ## Runs pylinter on all python project files
	black .
	ruff check --fix .
	pyright services/base
	pyright services/data_service/application
	pyright services/data_service/api
	pyright services/data_service/v1
	pyright services/data_service/v02
	pyright services/serverless/apps/data_consistency_validator
	pyright services/serverless/apps/notify_handler
	pyright services/serverless/apps/single_correlation_app
	pyright services/serverless/apps/trend_insights

migrate: rebuild ## Migrates the SQL databases
	docker run --network $(DOCKER_NETWORK) --rm user_service bash -c "cd ./services/base/infrastructure/database/sql_alchemy/migrations && alembic upgrade head"
	docker run --network $(DOCKER_NETWORK) --rm user_service python3 -m services.base.infrastructure.database.sql_alchemy.run_seeders

rebuild: ## Rebuilds all project dockerfiles and brings up the containers
	docker compose --profile $(RUN_ENV) -f infrastructure/docker-compose.yaml build
	docker compose --profile $(RUN_ENV) build

rebuild_and_launch_services: ## Rebuilds services and replaces them if something changed in them
	docker compose --profile $(RUN_ENV) up --detach --build

rebuild_and_launch_infrastructure: ## Rebuilds the infrastructure containers
	docker compose --profile $(RUN_ENV) -f infrastructure/docker-compose.yaml up --detach --build

reinitialize_indices: ## Renitializes all indicies
	docker exec data_service python3 -m services.base.infrastructure.database.opensearch.opensearch_initializer

restart: ## Restarts api services
	docker compose --profile $(RUN_ENV) restart

provision_infrastructure: launch_infrastructure ## sets up infrastructure resources
	docker build -f ./infrastructure/aws/Dockerfile -t aws_provisioner .
	docker run --rm --network $(DOCKER_NETWORK) \
	--env-file ./settings/.env.${RUN_ENV} -e AWS_ACCESS_KEY_ID=fake -e AWS_SECRET_ACCESS_KEY=fake \
		aws_provisioner \
		bash -c "cdklocal bootstrap --app=infrastructure/aws/cdk.py && \
		cdklocal deploy --app=infrastructure/aws/cdk.py --require-approval=never --method=direct"

test: rebuild build_lambdas_test_handler  ## Runs tests in services (specific for each service)
	make --file=./services/data_service/Makefile test_data_service
	make --file=./services/file_service/Makefile test_file_service
	make --file=./services/mobile_service/Makefile test_mobile_service
	make --file=./services/user_service/Makefile test_user_service
	make --file=./services/serverless/apps/Makefile test_serverless
	make --file=./services/serverless/apps/Makefile test_serverless_integration

venv: ## Collects all requirements and installs them into a .venv foldeor
	uv venv .venv
	find . -name "requirements.txt" -exec uv pip install --system -r {} \;
